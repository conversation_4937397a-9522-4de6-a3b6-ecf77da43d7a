model CreateMiniAppWidgetRequest {
  ...OmitProperties<BaseWidget, "id">;
  widgetType: WidgetType;
  config: unknown;
}

alias UpdateMiniAppWidgetRequest = Widgets;

alias MiniAppWidgetResponse = Widgets;

enum WidgetType {
  collection: "collection",
  title: "title",
  separator: "separator",
  text: "text",
  richText: "rich_text",
  alert: "alert",
  fields: "fields",
  image: "image",
  video: "video",
  bigNumber: "big_number",
  button: "button",
  link: "link",
  formContainer: "form_container",
  textEntry: "text_entry",
  dateTime: "date_time",
  date: "date",
  time: "time",
  numberEntry: "number_entry",
  phoneEntry: "phone_entry",
  emailEntry: "email_entry",
  checkbox: "checkbox",
  imagePicker: "image_picker",
  filePicker: "file_picker",
  choice: "choice",
}

alias FormChildrenWidget =
  | CollectionWidget
  | SeparatorWidget
  | TitleWidget
  | TextWidget
  | RichTextWidget
  | AlertWidget
  | FieldsWidget
  | ImageWidget
  | VideoWidget
  | BigNumberWidget
  | ButtonWidget
  | LinkWidget
  | TextEntryWidget
  | DateTimeWidget
  | DateWidget
  | TimeWidget
  | NumberEntryWidget
  | PhoneEntryWidget
  | EmailEntryWidget
  | CheckboxWidget
  | ImagePickerWidget
  | FilePickerWidget
  | ChoiceWidget;

alias Widgets = FormContainerWidget | FormChildrenWidget;

// Primary Data Widgets
model CollectionWidget extends BaseWidget {
  widgetType: WidgetType.collection;
  config: {
    bindingConfig: BindingConfig;
    style: "list" | "grid";
    itemsData: {
      title: string;
      subtitle: string;
      meta: string;
      image: string;
    };
    design: {
      size: "default" | "compact";
      style: "default" | "card";
      imageShape: "circle" | "square";
    };
    options: {
      limitItems?: int32;
    };
    pagination: {
      enabled: boolean;
      page?: string;
      limit?: string;
    };
  };
}

// Layout Widgets
model SeparatorWidget extends BaseWidget {
  widgetType: WidgetType.separator;
  config: {
    design: {
      size: "small" | "medium" | "large";
      drawLine?: boolean;
    };
  };
}

// Content Widgets
model TitleWidget extends BaseWidget {
  widgetType: WidgetType.title;
  config: {
    style: "Simple" | "Banner";
    data?: {
      title?: string;
      subtitle?: string;
      meta?: string;
      image?: string;
    };
    design: {
      imageFill: "fill" | "contain";
    };
  };
}

model TextWidget extends BaseWidget {
  widgetType: WidgetType.text;
  config: {
    content: string;
    design: {
      style:
        | "large"
        | "regular"
        | "small"
        | "footnote"
        | "meta_text"
        | "headline_xsmall"
        | "headline_small"
        | "headline_medium"
        | "headline_large"
        | "headline_xlarge";
      textAlign: "left" | "center" | "right";
    };
  };
}

model RichTextWidget extends BaseWidget {
  widgetType: WidgetType.richText;
  config: {
    content: string;
  };
}

model AlertWidget extends BaseWidget {
  widgetType: WidgetType.alert;
  config: {
    title: string;
    subtitle?: string;
    design: {
      type: "info" | "warning" | "error" | "success";
    };
  };
}

model FieldsWidget extends BaseWidget {
  widgetType: WidgetType.fields;
  config: {
    fields: {
      title: string;
      subtitle?: string;
      meta?: string;
      image?: string;
    }[];
    design: {
      style: "default" | "compact";
      imageShape: "circle" | "square";
    };
  };
}

model ImageWidget extends BaseWidget {
  widgetType: WidgetType.image;
  config: {
    content: string;
    design: {
      aspectRatio: "1:1" | "16:9" | "4:3" | "3:2";
      fill: "fill" | "contain";
      fullWidth?: boolean;
    };
  };
}

model VideoWidget extends BaseWidget {
  widgetType: WidgetType.video;
  config: {
    url: string;
    design?: {
      aspectRatio: "1:1" | "16:9" | "4:3" | "3:2";
      fullWidth?: boolean;
    };
  };
}

model BigNumberWidget extends BaseWidget {
  widgetType: WidgetType.bigNumber;
  config: {
    fields: {
      title: string;
      value?: string;
    }[];
    design?: {
      size?: "small" | "medium" | "large";
    };
  };
}

// Interactive Widgets
model ButtonWidget extends BaseWidget {
  widgetType: WidgetType.button;
  config: {
    buttons: Action[];
    design?: {
      primary?: "left" | "none" | "right";
    };
  };
}

model LinkWidget extends BaseWidget {
  widgetType: WidgetType.link;
  config: {
    links: Action[];
    design?: {
      size?: "left" | "none" | "right";
    };
  };
}

// Form Widgets
model FormContainerWidget extends BaseWidget {
  widgetType: WidgetType.formContainer;
  config: {
    title?: string;
    subtitle?: string;
    bindingConfig?: BindingConfig;
    widgets: FormChildrenWidget[];
    design?: {
      submitButtonText?: string;
    };
  };
}

model BaseInputWidget extends BaseWidget {
  config: {
    label: string;
    required: boolean;
    binding?: string;
  };
}

model TextEntryWidget extends BaseInputWidget {
  widgetType: WidgetType.textEntry;
}

model DateTimeWidget extends BaseInputWidget {
  widgetType: WidgetType.dateTime;
}

model DateWidget extends BaseInputWidget {
  widgetType: WidgetType.date;
}

model TimeWidget extends BaseInputWidget {
  widgetType: WidgetType.time;
}

model NumberEntryWidget extends BaseInputWidget {
  widgetType: WidgetType.numberEntry;
}

model PhoneEntryWidget extends BaseInputWidget {
  widgetType: WidgetType.phoneEntry;
}

model EmailEntryWidget extends BaseInputWidget {
  widgetType: WidgetType.emailEntry;
}

model CheckboxWidget extends BaseInputWidget {
  widgetType: WidgetType.checkbox;
}

model ImagePickerWidget extends BaseInputWidget {
  widgetType: WidgetType.imagePicker;
}

model FilePickerWidget extends BaseInputWidget {
  widgetType: WidgetType.filePicker;
}

model ChoiceWidget extends BaseInputWidget {
  widgetType: WidgetType.choice;
}

// Base Widget
model BaseWidget {
  id: int32;
  name: string;
  position: int32;
  isHidden: boolean;

  // Under consideration
  actionConfig?: string;

  bindingConfig?: string;
}

model BindingConfig {
  dataSourceId: numeric;
  externalApiId?: numeric;
}

// Actions Related
model Action {
  title: string;
  icon: string;
  actionType: ActionType;
}

enum ActionType {
  navigateToPage: "navigate_to_page",
  navigateBack: "navigate_back",
  openUrl: "open_url",
  submitForm: "submit_form",
}
