openapi: 3.0.0
info:
  title: Super App Framework Service
  version: 0.0.0
tags:
  - name: Ad<PERSON> - Auth
  - name: Admin - Users
  - name: Admin - Invitation
  - name: Admin - Mini app
  - name: Admin - Data Source
  - name: Admin - Mini App Version
  - name: Admin - Mini App Page
  - name: Admin - Mini App Widget
  - name: Customer Auth
paths:
  /api/admin/auth/login:
    post:
      operationId: Auth_login
      parameters: []
      responses:
        '200':
          description: The request has succeeded.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserLoginResponse'
        '400':
          description: The server could not understand the request due to invalid syntax.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '404':
          description: The server cannot find the requested resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
      tags:
        - Admin - Auth
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserLoginRequest'
  /api/admin/auth/me:
    get:
      operationId: Auth_me
      parameters: []
      responses:
        '200':
          description: The request has succeeded.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserResponse'
        '400':
          description: The server could not understand the request due to invalid syntax.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '404':
          description: The server cannot find the requested resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
      tags:
        - Admin - Auth
  /api/admin/auth/refresh-token:
    post:
      operationId: Auth_refreshToken
      parameters: []
      responses:
        '200':
          description: The request has succeeded.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RefreshTokenResponse'
        '400':
          description: The server could not understand the request due to invalid syntax.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '404':
          description: The server cannot find the requested resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
      tags:
        - Admin - Auth
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RefreshTokenRequest'
  /api/admin/invitations:
    post:
      operationId: Invitation_inviteToSystem
      parameters: []
      responses:
        '200':
          description: The request has succeeded.
        '400':
          description: The server could not understand the request due to invalid syntax.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '404':
          description: The server cannot find the requested resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
      tags:
        - Admin - Invitation
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/InviteToSystemRequest'
    get:
      operationId: Invitation_listInvitations
      parameters:
        - $ref: '#/components/parameters/PaginationParams.page'
        - $ref: '#/components/parameters/PaginationParams.limit'
        - $ref: '#/components/parameters/PaginationParams.sort'
      responses:
        '200':
          description: The request has succeeded.
          content:
            application/json:
              schema:
                type: object
                required:
                  - items
                  - totalCount
                  - page
                  - pageSize
                  - totalPages
                properties:
                  items:
                    type: array
                    items:
                      $ref: '#/components/schemas/InvitationResponse'
                  totalCount:
                    type: integer
                    format: int32
                  page:
                    type: integer
                    format: int32
                  pageSize:
                    type: integer
                    format: int32
                  totalPages:
                    type: integer
                    format: int32
        '400':
          description: The server could not understand the request due to invalid syntax.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '404':
          description: The server cannot find the requested resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
      tags:
        - Admin - Invitation
  /api/admin/invitations/{invitationId}/cancel:
    delete:
      operationId: Invitation_cancelInvitation
      parameters:
        - name: invitationId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      responses:
        '200':
          description: The request has succeeded.
        '400':
          description: The server could not understand the request due to invalid syntax.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '404':
          description: The server cannot find the requested resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
      tags:
        - Admin - Invitation
  /api/admin/invitations/{token}:
    get:
      operationId: Invitation_validateInvitation
      parameters:
        - name: token
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: The request has succeeded.
        '400':
          description: The server could not understand the request due to invalid syntax.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '404':
          description: The server cannot find the requested resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
      tags:
        - Admin - Invitation
  /api/admin/invitations/{token}/accept:
    post:
      operationId: Invitation_acceptInvitation
      parameters:
        - name: token
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: The request has succeeded.
        '400':
          description: The server could not understand the request due to invalid syntax.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '404':
          description: The server cannot find the requested resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
      tags:
        - Admin - Invitation
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AcceptInvitationRequest'
  /api/admin/teams/{teamId}/invitations:
    post:
      operationId: Invitation_inviteToTeam
      parameters:
        - name: teamId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      responses:
        '200':
          description: The request has succeeded.
        '400':
          description: The server could not understand the request due to invalid syntax.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '404':
          description: The server cannot find the requested resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
      tags:
        - Admin - Invitation
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/InviteToTeamRequest'
    get:
      operationId: Invitation_listTeamInvitations
      parameters:
        - name: teamId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - $ref: '#/components/parameters/PaginationParams.page'
        - $ref: '#/components/parameters/PaginationParams.limit'
        - $ref: '#/components/parameters/PaginationParams.sort'
      responses:
        '200':
          description: The request has succeeded.
          content:
            application/json:
              schema:
                type: object
                required:
                  - items
                  - totalCount
                  - page
                  - pageSize
                  - totalPages
                properties:
                  items:
                    type: array
                    items:
                      $ref: '#/components/schemas/InvitationResponse'
                  totalCount:
                    type: integer
                    format: int32
                  page:
                    type: integer
                    format: int32
                  pageSize:
                    type: integer
                    format: int32
                  totalPages:
                    type: integer
                    format: int32
        '400':
          description: The server could not understand the request due to invalid syntax.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '404':
          description: The server cannot find the requested resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
      tags:
        - Admin - Invitation
  /api/admin/teams/{teamId}/invitations/{invitationId}/cancel:
    delete:
      operationId: Invitation_cancelTeamInvitation
      parameters:
        - name: teamId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: invitationId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      responses:
        '200':
          description: The request has succeeded.
        '400':
          description: The server could not understand the request due to invalid syntax.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '404':
          description: The server cannot find the requested resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
      tags:
        - Admin - Invitation
  /api/admin/teams/{teamId}/mini-apps:
    post:
      operationId: MiniApp_createMiniApp
      parameters:
        - name: teamId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      responses:
        '201':
          description: The request has succeeded and a new resource has been created as a result.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MiniApp'
        '400':
          description: The server could not understand the request due to invalid syntax.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '404':
          description: The server cannot find the requested resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
      tags:
        - Admin - Mini app
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateMiniAppRequest'
    get:
      operationId: MiniApp_listMiniApps
      parameters:
        - name: teamId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - $ref: '#/components/parameters/PaginationParams.page'
        - $ref: '#/components/parameters/PaginationParams.limit'
        - $ref: '#/components/parameters/PaginationParams.sort'
      responses:
        '200':
          description: The request has succeeded.
          content:
            application/json:
              schema:
                type: object
                required:
                  - items
                  - totalCount
                  - page
                  - pageSize
                  - totalPages
                properties:
                  items:
                    type: array
                    items:
                      $ref: '#/components/schemas/MiniApp'
                  totalCount:
                    type: integer
                    format: int32
                  page:
                    type: integer
                    format: int32
                  pageSize:
                    type: integer
                    format: int32
                  totalPages:
                    type: integer
                    format: int32
        '400':
          description: The server could not understand the request due to invalid syntax.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '404':
          description: The server cannot find the requested resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
      tags:
        - Admin - Mini app
  /api/admin/teams/{teamId}/mini-apps/{miniAppId}:
    get:
      operationId: MiniApp_getMiniApp
      parameters:
        - name: teamId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: miniAppId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      responses:
        '200':
          description: The request has succeeded.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MiniApp'
        '400':
          description: The server could not understand the request due to invalid syntax.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '404':
          description: The server cannot find the requested resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
      tags:
        - Admin - Mini app
    patch:
      operationId: MiniApp_updateMiniApp
      parameters:
        - name: teamId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: miniAppId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      responses:
        '200':
          description: The request has succeeded.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MiniApp'
        '400':
          description: The server could not understand the request due to invalid syntax.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '404':
          description: The server cannot find the requested resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
      tags:
        - Admin - Mini app
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateMiniAppRequest'
    delete:
      operationId: MiniApp_deleteMiniApp
      parameters:
        - name: teamId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: miniAppId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      responses:
        '204':
          description: 'There is no content to send for this request, but the headers may be useful. '
        '400':
          description: The server could not understand the request due to invalid syntax.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '404':
          description: The server cannot find the requested resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
      tags:
        - Admin - Mini app
  /api/admin/teams/{teamId}/mini-apps/{miniAppId}/data-sources:
    post:
      operationId: DataSource_create
      parameters:
        - name: teamId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: miniAppId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      responses:
        '201':
          description: The request has succeeded and a new resource has been created as a result.
          content:
            application/json:
              schema:
                anyOf:
                  - $ref: '#/components/schemas/InternalTableDataSource'
                  - $ref: '#/components/schemas/ExternalApiDataSource'
        '400':
          description: The server could not understand the request due to invalid syntax.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '404':
          description: The server cannot find the requested resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
      tags:
        - Admin - Data Source
      requestBody:
        required: true
        content:
          application/json:
            schema:
              anyOf:
                - $ref: '#/components/schemas/CreateExternalApiDataSourceRequest'
                - $ref: '#/components/schemas/CreateInternalTableDataSourceRequest'
    get:
      operationId: DataSource_list
      parameters:
        - name: teamId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: miniAppId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - $ref: '#/components/parameters/PaginationParams.page'
        - $ref: '#/components/parameters/PaginationParams.limit'
        - $ref: '#/components/parameters/PaginationParams.sort'
      responses:
        '200':
          description: The request has succeeded.
          content:
            application/json:
              schema:
                type: object
                required:
                  - items
                  - totalCount
                  - page
                  - pageSize
                  - totalPages
                properties:
                  items:
                    type: array
                    items:
                      anyOf:
                        - $ref: '#/components/schemas/InternalTableDataSource'
                        - $ref: '#/components/schemas/ExternalApiDataSource'
                  totalCount:
                    type: integer
                    format: int32
                  page:
                    type: integer
                    format: int32
                  pageSize:
                    type: integer
                    format: int32
                  totalPages:
                    type: integer
                    format: int32
        '400':
          description: The server could not understand the request due to invalid syntax.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '404':
          description: The server cannot find the requested resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
      tags:
        - Admin - Data Source
  /api/admin/teams/{teamId}/mini-apps/{miniAppId}/data-sources/{dataSourceId}:
    get:
      operationId: DataSource_get
      parameters:
        - name: teamId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: miniAppId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: dataSourceId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      responses:
        '200':
          description: The request has succeeded.
          content:
            application/json:
              schema:
                anyOf:
                  - $ref: '#/components/schemas/InternalTableDataSource'
                  - $ref: '#/components/schemas/ExternalApiDataSource'
        '400':
          description: The server could not understand the request due to invalid syntax.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '404':
          description: The server cannot find the requested resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
      tags:
        - Admin - Data Source
    patch:
      operationId: DataSource_update
      parameters:
        - name: teamId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: miniAppId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: dataSourceId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      responses:
        '200':
          description: The request has succeeded.
          content:
            application/json:
              schema:
                anyOf:
                  - $ref: '#/components/schemas/InternalTableDataSource'
                  - $ref: '#/components/schemas/ExternalApiDataSource'
        '400':
          description: The server could not understand the request due to invalid syntax.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '404':
          description: The server cannot find the requested resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
      tags:
        - Admin - Data Source
      requestBody:
        required: true
        content:
          application/json:
            schema:
              anyOf:
                - $ref: '#/components/schemas/UpdateExternalApiDataSourceRequest'
                - $ref: '#/components/schemas/UpdateInternalTableDataSourceRequest'
    delete:
      operationId: DataSource_delete
      parameters:
        - name: teamId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: miniAppId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: dataSourceId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      responses:
        '204':
          description: 'There is no content to send for this request, but the headers may be useful. '
        '400':
          description: The server could not understand the request due to invalid syntax.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '404':
          description: The server cannot find the requested resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
      tags:
        - Admin - Data Source
  /api/admin/teams/{teamId}/mini-apps/{miniAppId}/data-sources/{dataSourceId}/external-apis:
    post:
      operationId: ExternalApi_create
      parameters:
        - name: teamId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: miniAppId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: dataSourceId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      responses:
        '201':
          description: The request has succeeded and a new resource has been created as a result.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExternalApiResponse'
        '400':
          description: The server could not understand the request due to invalid syntax.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '404':
          description: The server cannot find the requested resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
      tags:
        - Admin - Data Source
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateExternalApiRequest'
    get:
      operationId: ExternalApi_list
      parameters:
        - name: teamId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: miniAppId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: dataSourceId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - $ref: '#/components/parameters/PaginationParams.page'
        - $ref: '#/components/parameters/PaginationParams.limit'
        - $ref: '#/components/parameters/PaginationParams.sort'
      responses:
        '200':
          description: The request has succeeded.
          content:
            application/json:
              schema:
                type: object
                required:
                  - items
                  - totalCount
                  - page
                  - pageSize
                  - totalPages
                properties:
                  items:
                    type: array
                    items:
                      $ref: '#/components/schemas/ExternalApiResponse'
                  totalCount:
                    type: integer
                    format: int32
                  page:
                    type: integer
                    format: int32
                  pageSize:
                    type: integer
                    format: int32
                  totalPages:
                    type: integer
                    format: int32
        '400':
          description: The server could not understand the request due to invalid syntax.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '404':
          description: The server cannot find the requested resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
      tags:
        - Admin - Data Source
  /api/admin/teams/{teamId}/mini-apps/{miniAppId}/data-sources/{dataSourceId}/external-apis/{externalApiId}:
    get:
      operationId: ExternalApi_get
      parameters:
        - name: teamId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: miniAppId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: dataSourceId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: externalApiId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      responses:
        '200':
          description: The request has succeeded.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExternalApiResponse'
        '400':
          description: The server could not understand the request due to invalid syntax.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '404':
          description: The server cannot find the requested resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
      tags:
        - Admin - Data Source
    patch:
      operationId: ExternalApi_update
      parameters:
        - name: teamId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: miniAppId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: dataSourceId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: externalApiId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      responses:
        '200':
          description: The request has succeeded.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExternalApiResponse'
        '400':
          description: The server could not understand the request due to invalid syntax.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '404':
          description: The server cannot find the requested resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
      tags:
        - Admin - Data Source
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateExternalApiRequest'
    delete:
      operationId: ExternalApi_delete
      parameters:
        - name: teamId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: miniAppId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: dataSourceId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: externalApiId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      responses:
        '204':
          description: 'There is no content to send for this request, but the headers may be useful. '
        '400':
          description: The server could not understand the request due to invalid syntax.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '404':
          description: The server cannot find the requested resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
      tags:
        - Admin - Data Source
  /api/admin/teams/{teamId}/mini-apps/{miniAppId}/data-sources/{dataSourceId}/external-apis/{externalApiId}/execute:
    get:
      operationId: ExternalApi_execute
      parameters:
        - name: teamId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: miniAppId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: dataSourceId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: externalApiId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      responses:
        '200':
          description: The request has succeeded.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExternalApiResponseSnapshot'
        '400':
          description: The server could not understand the request due to invalid syntax.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '404':
          description: The server cannot find the requested resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
      tags:
        - Admin - Data Source
  /api/admin/teams/{teamId}/mini-apps/{miniAppId}/data-sources/{dataSourceId}/external-apis/{externalApiId}/external-api-snapshots:
    get:
      operationId: ExternalApi_getSnapshots
      parameters:
        - name: teamId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: miniAppId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: dataSourceId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: externalApiId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      responses:
        '200':
          description: The request has succeeded.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExternalApiResponseSnapshot'
        '400':
          description: The server could not understand the request due to invalid syntax.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '404':
          description: The server cannot find the requested resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
      tags:
        - Admin - Data Source
  /api/admin/teams/{teamId}/mini-apps/{miniAppId}/mini-app-versions:
    post:
      operationId: MiniAppVersion_create
      parameters:
        - name: teamId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: miniAppId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      responses:
        '201':
          description: The request has succeeded and a new resource has been created as a result.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MiniAppVersionDetail'
        '400':
          description: The server could not understand the request due to invalid syntax.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '404':
          description: The server cannot find the requested resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
      tags:
        - Admin - Mini App Version
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateMiniAppVersionRequest'
    get:
      operationId: MiniAppVersion_list
      parameters:
        - name: teamId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: miniAppId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - $ref: '#/components/parameters/PaginationParams.page'
        - $ref: '#/components/parameters/PaginationParams.limit'
        - $ref: '#/components/parameters/PaginationParams.sort'
      responses:
        '200':
          description: The request has succeeded.
          content:
            application/json:
              schema:
                type: object
                required:
                  - items
                  - totalCount
                  - page
                  - pageSize
                  - totalPages
                properties:
                  items:
                    type: array
                    items:
                      $ref: '#/components/schemas/MiniAppVersion'
                  totalCount:
                    type: integer
                    format: int32
                  page:
                    type: integer
                    format: int32
                  pageSize:
                    type: integer
                    format: int32
                  totalPages:
                    type: integer
                    format: int32
        '400':
          description: The server could not understand the request due to invalid syntax.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '404':
          description: The server cannot find the requested resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
      tags:
        - Admin - Mini App Version
  /api/admin/teams/{teamId}/mini-apps/{miniAppId}/mini-app-versions/{versionId}:
    get:
      operationId: MiniAppVersion_get
      parameters:
        - name: teamId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: miniAppId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: versionId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      responses:
        '200':
          description: The request has succeeded.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MiniAppVersionDetail'
        '400':
          description: The server could not understand the request due to invalid syntax.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '404':
          description: The server cannot find the requested resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
      tags:
        - Admin - Mini App Version
    patch:
      operationId: MiniAppVersion_update
      parameters:
        - name: teamId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: miniAppId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: versionId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      responses:
        '200':
          description: The request has succeeded.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MiniAppVersionDetail'
        '400':
          description: The server could not understand the request due to invalid syntax.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '404':
          description: The server cannot find the requested resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
      tags:
        - Admin - Mini App Version
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateMiniAppVersionRequest'
    delete:
      operationId: MiniAppVersion_delete
      parameters:
        - name: teamId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: miniAppId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: versionId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      responses:
        '204':
          description: 'There is no content to send for this request, but the headers may be useful. '
        '400':
          description: The server could not understand the request due to invalid syntax.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '404':
          description: The server cannot find the requested resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
      tags:
        - Admin - Mini App Version
  /api/admin/teams/{teamId}/mini-apps/{miniAppId}/mini-app-versions/{versionId}/pages:
    post:
      operationId: MiniAppPage_create
      parameters:
        - name: teamId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: miniAppId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: versionId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      responses:
        '201':
          description: The request has succeeded and a new resource has been created as a result.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MiniAppPage'
        '400':
          description: The server could not understand the request due to invalid syntax.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '404':
          description: The server cannot find the requested resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
      tags:
        - Admin - Mini App Page
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateMiniAppPageRequest'
  /api/admin/teams/{teamId}/mini-apps/{miniAppId}/mini-app-versions/{versionId}/pages/{pageId}:
    patch:
      operationId: MiniAppPage_update
      parameters:
        - name: teamId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: miniAppId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: versionId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: pageId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      responses:
        '200':
          description: The request has succeeded.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MiniAppPage'
        '400':
          description: The server could not understand the request due to invalid syntax.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '404':
          description: The server cannot find the requested resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
      tags:
        - Admin - Mini App Page
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateMiniAppPageRequest'
    delete:
      operationId: MiniAppPage_delete
      parameters:
        - name: teamId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: miniAppId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: versionId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: pageId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      responses:
        '204':
          description: 'There is no content to send for this request, but the headers may be useful. '
        '400':
          description: The server could not understand the request due to invalid syntax.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '404':
          description: The server cannot find the requested resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
      tags:
        - Admin - Mini App Page
  /api/admin/teams/{teamId}/mini-apps/{miniAppId}/mini-app-versions/{versionId}/pages/{pageId}/move:
    patch:
      operationId: MiniAppPage_move
      parameters:
        - name: teamId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: miniAppId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: versionId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: pageId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      responses:
        '200':
          description: The request has succeeded.
        '400':
          description: The server could not understand the request due to invalid syntax.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '404':
          description: The server cannot find the requested resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
      tags:
        - Admin - Mini App Page
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MoveRequest'
  /api/admin/teams/{teamId}/mini-apps/{miniAppId}/mini-app-versions/{versionId}/pages/{pageId}/widgets:
    post:
      operationId: MiniAppWidget_create
      parameters:
        - name: teamId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: miniAppId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: versionId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: pageId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      responses:
        '201':
          description: The request has succeeded and a new resource has been created as a result.
          content:
            application/json:
              schema:
                anyOf:
                  - $ref: '#/components/schemas/FormContainerWidget'
                  - $ref: '#/components/schemas/CollectionWidget'
                  - $ref: '#/components/schemas/SeparatorWidget'
                  - $ref: '#/components/schemas/TitleWidget'
                  - $ref: '#/components/schemas/TextWidget'
                  - $ref: '#/components/schemas/RichTextWidget'
                  - $ref: '#/components/schemas/AlertWidget'
                  - $ref: '#/components/schemas/FieldsWidget'
                  - $ref: '#/components/schemas/ImageWidget'
                  - $ref: '#/components/schemas/VideoWidget'
                  - $ref: '#/components/schemas/BigNumberWidget'
                  - $ref: '#/components/schemas/ButtonWidget'
                  - $ref: '#/components/schemas/LinkWidget'
                  - $ref: '#/components/schemas/TextEntryWidget'
                  - $ref: '#/components/schemas/DateTimeWidget'
                  - $ref: '#/components/schemas/DateWidget'
                  - $ref: '#/components/schemas/TimeWidget'
                  - $ref: '#/components/schemas/NumberEntryWidget'
                  - $ref: '#/components/schemas/PhoneEntryWidget'
                  - $ref: '#/components/schemas/EmailEntryWidget'
                  - $ref: '#/components/schemas/CheckboxWidget'
                  - $ref: '#/components/schemas/ImagePickerWidget'
                  - $ref: '#/components/schemas/FilePickerWidget'
                  - $ref: '#/components/schemas/ChoiceWidget'
        '400':
          description: The server could not understand the request due to invalid syntax.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '404':
          description: The server cannot find the requested resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
      tags:
        - Admin - Mini App Widget
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateMiniAppWidgetRequest'
  /api/admin/teams/{teamId}/mini-apps/{miniAppId}/mini-app-versions/{versionId}/pages/{pageId}/widgets/{widgetId}:
    patch:
      operationId: MiniAppWidget_update
      parameters:
        - name: teamId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: miniAppId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: versionId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: pageId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: widgetId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      responses:
        '200':
          description: The request has succeeded.
          content:
            application/json:
              schema:
                anyOf:
                  - $ref: '#/components/schemas/FormContainerWidget'
                  - $ref: '#/components/schemas/CollectionWidget'
                  - $ref: '#/components/schemas/SeparatorWidget'
                  - $ref: '#/components/schemas/TitleWidget'
                  - $ref: '#/components/schemas/TextWidget'
                  - $ref: '#/components/schemas/RichTextWidget'
                  - $ref: '#/components/schemas/AlertWidget'
                  - $ref: '#/components/schemas/FieldsWidget'
                  - $ref: '#/components/schemas/ImageWidget'
                  - $ref: '#/components/schemas/VideoWidget'
                  - $ref: '#/components/schemas/BigNumberWidget'
                  - $ref: '#/components/schemas/ButtonWidget'
                  - $ref: '#/components/schemas/LinkWidget'
                  - $ref: '#/components/schemas/TextEntryWidget'
                  - $ref: '#/components/schemas/DateTimeWidget'
                  - $ref: '#/components/schemas/DateWidget'
                  - $ref: '#/components/schemas/TimeWidget'
                  - $ref: '#/components/schemas/NumberEntryWidget'
                  - $ref: '#/components/schemas/PhoneEntryWidget'
                  - $ref: '#/components/schemas/EmailEntryWidget'
                  - $ref: '#/components/schemas/CheckboxWidget'
                  - $ref: '#/components/schemas/ImagePickerWidget'
                  - $ref: '#/components/schemas/FilePickerWidget'
                  - $ref: '#/components/schemas/ChoiceWidget'
        '400':
          description: The server could not understand the request due to invalid syntax.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '404':
          description: The server cannot find the requested resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
      tags:
        - Admin - Mini App Widget
      requestBody:
        required: true
        content:
          application/json:
            schema:
              anyOf:
                - $ref: '#/components/schemas/FormContainerWidgetUpdate'
                - $ref: '#/components/schemas/CollectionWidgetUpdate'
                - $ref: '#/components/schemas/SeparatorWidgetUpdate'
                - $ref: '#/components/schemas/TitleWidgetUpdate'
                - $ref: '#/components/schemas/TextWidgetUpdate'
                - $ref: '#/components/schemas/RichTextWidgetUpdate'
                - $ref: '#/components/schemas/AlertWidgetUpdate'
                - $ref: '#/components/schemas/FieldsWidgetUpdate'
                - $ref: '#/components/schemas/ImageWidgetUpdate'
                - $ref: '#/components/schemas/VideoWidgetUpdate'
                - $ref: '#/components/schemas/BigNumberWidgetUpdate'
                - $ref: '#/components/schemas/ButtonWidgetUpdate'
                - $ref: '#/components/schemas/LinkWidgetUpdate'
                - $ref: '#/components/schemas/TextEntryWidgetUpdate'
                - $ref: '#/components/schemas/DateTimeWidgetUpdate'
                - $ref: '#/components/schemas/DateWidgetUpdate'
                - $ref: '#/components/schemas/TimeWidgetUpdate'
                - $ref: '#/components/schemas/NumberEntryWidgetUpdate'
                - $ref: '#/components/schemas/PhoneEntryWidgetUpdate'
                - $ref: '#/components/schemas/EmailEntryWidgetUpdate'
                - $ref: '#/components/schemas/CheckboxWidgetUpdate'
                - $ref: '#/components/schemas/ImagePickerWidgetUpdate'
                - $ref: '#/components/schemas/FilePickerWidgetUpdate'
                - $ref: '#/components/schemas/ChoiceWidgetUpdate'
    delete:
      operationId: MiniAppWidget_delete
      parameters:
        - name: teamId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: miniAppId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: versionId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: pageId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: widgetId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      responses:
        '204':
          description: 'There is no content to send for this request, but the headers may be useful. '
        '400':
          description: The server could not understand the request due to invalid syntax.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '404':
          description: The server cannot find the requested resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
      tags:
        - Admin - Mini App Widget
  /api/admin/teams/{teamId}/mini-apps/{miniAppId}/mini-app-versions/{versionId}/pages/{pageId}/widgets/{widgetId}/move:
    patch:
      operationId: MiniAppWidget_move
      parameters:
        - name: teamId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: miniAppId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: versionId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: pageId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: widgetId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      responses:
        '200':
          description: The request has succeeded.
        '400':
          description: The server could not understand the request due to invalid syntax.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '404':
          description: The server cannot find the requested resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
      tags:
        - Admin - Mini App Widget
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MoveRequest'
  /api/admin/teams/{teamId}/users:
    get:
      operationId: User_listTeamUsers
      parameters:
        - name: teamId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: email
          in: query
          required: false
          schema:
            type: string
          explode: false
        - name: name
          in: query
          required: false
          schema:
            type: string
          explode: false
        - $ref: '#/components/parameters/PaginationParams.page'
        - $ref: '#/components/parameters/PaginationParams.limit'
        - $ref: '#/components/parameters/PaginationParams.sort'
      responses:
        '200':
          description: The request has succeeded.
          content:
            application/json:
              schema:
                type: object
                required:
                  - items
                  - totalCount
                  - page
                  - pageSize
                  - totalPages
                properties:
                  items:
                    type: array
                    items:
                      $ref: '#/components/schemas/TeamUserResponse'
                  totalCount:
                    type: integer
                    format: int32
                  page:
                    type: integer
                    format: int32
                  pageSize:
                    type: integer
                    format: int32
                  totalPages:
                    type: integer
                    format: int32
        '400':
          description: The server could not understand the request due to invalid syntax.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '404':
          description: The server cannot find the requested resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
      tags:
        - Admin - Users
  /api/admin/teams/{teamId}/users/{userId}:
    get:
      operationId: User_getTeamUser
      parameters:
        - name: teamId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: userId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      responses:
        '200':
          description: The request has succeeded.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TeamUserResponse'
        '400':
          description: The server could not understand the request due to invalid syntax.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '404':
          description: The server cannot find the requested resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
      tags:
        - Admin - Users
    patch:
      operationId: User_updateTeamUser
      parameters:
        - name: teamId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: userId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      responses:
        '200':
          description: The request has succeeded.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TeamUserResponse'
        '400':
          description: The server could not understand the request due to invalid syntax.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '404':
          description: The server cannot find the requested resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
      tags:
        - Admin - Users
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TeamUserResponseUpdate'
    delete:
      operationId: User_deleteTeamUser
      parameters:
        - name: teamId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: userId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      responses:
        '204':
          description: 'There is no content to send for this request, but the headers may be useful. '
        '400':
          description: The server could not understand the request due to invalid syntax.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '404':
          description: The server cannot find the requested resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
      tags:
        - Admin - Users
  /api/admin/users:
    get:
      operationId: User_listUsers
      parameters:
        - $ref: '#/components/parameters/PaginationParams.page'
        - $ref: '#/components/parameters/PaginationParams.limit'
        - $ref: '#/components/parameters/PaginationParams.sort'
      responses:
        '200':
          description: The request has succeeded.
          content:
            application/json:
              schema:
                type: object
                required:
                  - items
                  - totalCount
                  - page
                  - pageSize
                  - totalPages
                properties:
                  items:
                    type: array
                    items:
                      $ref: '#/components/schemas/UserResponse'
                  totalCount:
                    type: integer
                    format: int32
                  page:
                    type: integer
                    format: int32
                  pageSize:
                    type: integer
                    format: int32
                  totalPages:
                    type: integer
                    format: int32
        '400':
          description: The server could not understand the request due to invalid syntax.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '404':
          description: The server cannot find the requested resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
      tags:
        - Admin - Users
  /api/admin/users/{userId}:
    get:
      operationId: User_getUser
      parameters:
        - name: userId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      responses:
        '200':
          description: The request has succeeded.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserResponse'
        '400':
          description: The server could not understand the request due to invalid syntax.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '404':
          description: The server cannot find the requested resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
      tags:
        - Admin - Users
    patch:
      operationId: User_updateUser
      parameters:
        - name: userId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      responses:
        '200':
          description: The request has succeeded.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserResponse'
        '400':
          description: The server could not understand the request due to invalid syntax.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '404':
          description: The server cannot find the requested resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
      tags:
        - Admin - Users
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateUserRequest'
    delete:
      operationId: User_deleteUser
      parameters:
        - name: userId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      responses:
        '204':
          description: 'There is no content to send for this request, but the headers may be useful. '
        '400':
          description: The server could not understand the request due to invalid syntax.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '404':
          description: The server cannot find the requested resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
      tags:
        - Admin - Users
  /api/auth/refresh-token:
    post:
      operationId: Auth_refreshToken
      parameters: []
      responses:
        '200':
          description: The request has succeeded.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserLoginResponse'
      tags:
        - Customer Auth
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RefreshTokenRequest'
components:
  parameters:
    PaginationParams.limit:
      name: limit
      in: query
      required: false
      schema:
        type: string
        default: '20'
      explode: false
    PaginationParams.page:
      name: page
      in: query
      required: false
      schema:
        type: string
        default: '1'
      explode: false
    PaginationParams.sort:
      name: sort
      in: query
      required: false
      schema:
        type: string
      explode: false
  schemas:
    AcceptInvitationRequest:
      type: object
      required:
        - name
        - password
      properties:
        name:
          type: string
        password:
          type: string
        confirmPassword:
          type: string
    Action:
      type: object
      required:
        - title
        - icon
        - actionType
      properties:
        title:
          type: string
        icon:
          type: string
        actionType:
          $ref: '#/components/schemas/ActionType'
    ActionType:
      type: string
      enum:
        - navigate_to_page
        - navigate_back
        - open_url
        - submit_form
    AlertWidget:
      type: object
      required:
        - widgetType
        - config
      properties:
        widgetType:
          type: string
          enum:
            - alert
        config:
          type: object
          properties:
            title:
              type: string
            subtitle:
              type: string
            design:
              type: object
              properties:
                type:
                  type: string
                  enum:
                    - info
                    - warning
                    - error
                    - success
              required:
                - type
          required:
            - title
            - design
      allOf:
        - $ref: '#/components/schemas/BaseWidget'
    AlertWidgetUpdate:
      type: object
      properties:
        widgetType:
          type: string
          enum:
            - alert
        config:
          type: object
          properties:
            title:
              type: string
            subtitle:
              type: string
            design:
              type: object
              properties:
                type:
                  type: string
                  enum:
                    - info
                    - warning
                    - error
                    - success
      allOf:
        - $ref: '#/components/schemas/BaseWidgetUpdate'
    BaseDataSource:
      type: object
      required:
        - id
        - name
        - dataSourceType
        - createdAt
        - createdBy
      properties:
        id:
          type: integer
          format: int32
        name:
          type: string
        dataSourceType:
          $ref: '#/components/schemas/DataSourceType'
        description:
          type: string
        createdAt:
          type: string
          format: date-time
        createdBy:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
        updatedBy:
          type: string
          format: date-time
    BaseInputWidget:
      type: object
      required:
        - config
      properties:
        config:
          type: object
          properties:
            label:
              type: string
            required:
              type: boolean
            binding:
              type: string
          required:
            - label
            - required
      allOf:
        - $ref: '#/components/schemas/BaseWidget'
    BaseInputWidgetUpdate:
      type: object
      properties:
        config:
          type: object
          properties:
            label:
              type: string
            required:
              type: boolean
            binding:
              type: string
      allOf:
        - $ref: '#/components/schemas/BaseWidgetUpdate'
    BaseUser:
      type: object
      required:
        - id
        - email
        - userStatus
        - createdAt
      properties:
        id:
          type: integer
          format: int64
        email:
          type: string
        name:
          type: string
        userStatus:
          $ref: '#/components/schemas/UserStatus'
        createdAt:
          type: string
          format: date-time
    BaseUserUpdate:
      type: object
      properties:
        id:
          type: integer
          format: int64
        email:
          type: string
        name:
          type: string
        userStatus:
          $ref: '#/components/schemas/UserStatus'
        createdAt:
          type: string
          format: date-time
    BaseWidget:
      type: object
      required:
        - id
        - name
        - position
        - isHidden
      properties:
        id:
          type: integer
          format: int32
        name:
          type: string
        position:
          type: integer
          format: int32
        isHidden:
          type: boolean
        actionConfig:
          type: string
        bindingConfig:
          type: string
    BaseWidgetUpdate:
      type: object
      properties:
        id:
          type: integer
          format: int32
        name:
          type: string
        position:
          type: integer
          format: int32
        isHidden:
          type: boolean
        actionConfig:
          type: string
        bindingConfig:
          type: string
    BigNumberWidget:
      type: object
      required:
        - widgetType
        - config
      properties:
        widgetType:
          type: string
          enum:
            - big_number
        config:
          type: object
          properties:
            fields:
              type: array
              items:
                type: object
                properties:
                  title:
                    type: string
                  value:
                    type: string
                required:
                  - title
            design:
              type: object
              properties:
                size:
                  type: string
                  enum:
                    - small
                    - medium
                    - large
          required:
            - fields
      allOf:
        - $ref: '#/components/schemas/BaseWidget'
    BigNumberWidgetUpdate:
      type: object
      properties:
        widgetType:
          type: string
          enum:
            - big_number
        config:
          type: object
          properties:
            fields:
              type: array
              items:
                type: object
                properties:
                  title:
                    type: string
                  value:
                    type: string
                required:
                  - title
            design:
              type: object
              properties:
                size:
                  type: string
                  enum:
                    - small
                    - medium
                    - large
      allOf:
        - $ref: '#/components/schemas/BaseWidgetUpdate'
    BindingConfig:
      type: object
      required:
        - dataSourceId
      properties:
        dataSourceId:
          type: number
        externalApiId:
          type: number
    BindingConfigUpdate:
      type: object
      properties:
        dataSourceId:
          type: number
        externalApiId:
          type: number
    ButtonWidget:
      type: object
      required:
        - widgetType
        - config
      properties:
        widgetType:
          type: string
          enum:
            - button
        config:
          type: object
          properties:
            buttons:
              type: array
              items:
                $ref: '#/components/schemas/Action'
            design:
              type: object
              properties:
                primary:
                  type: string
                  enum:
                    - left
                    - none
                    - right
          required:
            - buttons
      allOf:
        - $ref: '#/components/schemas/BaseWidget'
    ButtonWidgetUpdate:
      type: object
      properties:
        widgetType:
          type: string
          enum:
            - button
        config:
          type: object
          properties:
            buttons:
              type: array
              items:
                $ref: '#/components/schemas/Action'
            design:
              type: object
              properties:
                primary:
                  type: string
                  enum:
                    - left
                    - none
                    - right
      allOf:
        - $ref: '#/components/schemas/BaseWidgetUpdate'
    CheckboxWidget:
      type: object
      required:
        - widgetType
      properties:
        widgetType:
          type: string
          enum:
            - checkbox
      allOf:
        - $ref: '#/components/schemas/BaseInputWidget'
    CheckboxWidgetUpdate:
      type: object
      properties:
        widgetType:
          type: string
          enum:
            - checkbox
      allOf:
        - $ref: '#/components/schemas/BaseInputWidgetUpdate'
    ChoiceWidget:
      type: object
      required:
        - widgetType
      properties:
        widgetType:
          type: string
          enum:
            - choice
      allOf:
        - $ref: '#/components/schemas/BaseInputWidget'
    ChoiceWidgetUpdate:
      type: object
      properties:
        widgetType:
          type: string
          enum:
            - choice
      allOf:
        - $ref: '#/components/schemas/BaseInputWidgetUpdate'
    CollectionWidget:
      type: object
      required:
        - widgetType
        - config
      properties:
        widgetType:
          type: string
          enum:
            - collection
        config:
          type: object
          properties:
            bindingConfig:
              $ref: '#/components/schemas/BindingConfig'
            style:
              type: string
              enum:
                - list
                - grid
            itemsData:
              type: object
              properties:
                title:
                  type: string
                subtitle:
                  type: string
                meta:
                  type: string
                image:
                  type: string
              required:
                - title
                - subtitle
                - meta
                - image
            design:
              type: object
              properties:
                size:
                  type: string
                  enum:
                    - default
                    - compact
                style:
                  type: string
                  enum:
                    - default
                    - card
                imageShape:
                  type: string
                  enum:
                    - circle
                    - square
              required:
                - size
                - style
                - imageShape
            options:
              type: object
              properties:
                limitItems:
                  type: integer
                  format: int32
            pagination:
              type: object
              properties:
                enabled:
                  type: boolean
                page:
                  type: string
                limit:
                  type: string
              required:
                - enabled
          required:
            - bindingConfig
            - style
            - itemsData
            - design
            - options
            - pagination
      allOf:
        - $ref: '#/components/schemas/BaseWidget'
    CollectionWidgetUpdate:
      type: object
      properties:
        widgetType:
          type: string
          enum:
            - collection
        config:
          type: object
          properties:
            bindingConfig:
              $ref: '#/components/schemas/BindingConfigUpdate'
            style:
              type: string
              enum:
                - list
                - grid
            itemsData:
              type: object
              properties:
                title:
                  type: string
                subtitle:
                  type: string
                meta:
                  type: string
                image:
                  type: string
            design:
              type: object
              properties:
                size:
                  type: string
                  enum:
                    - default
                    - compact
                style:
                  type: string
                  enum:
                    - default
                    - card
                imageShape:
                  type: string
                  enum:
                    - circle
                    - square
            options:
              type: object
              properties:
                limitItems:
                  type: integer
                  format: int32
            pagination:
              type: object
              properties:
                enabled:
                  type: boolean
                page:
                  type: string
                limit:
                  type: string
      allOf:
        - $ref: '#/components/schemas/BaseWidgetUpdate'
    CreateExternalApiDataSourceRequest:
      type: object
      required:
        - dataSourceType
        - name
      properties:
        dataSourceType:
          type: string
          enum:
            - external_api
        config:
          $ref: '#/components/schemas/ExternalApiConfig'
        name:
          type: string
        description:
          type: string
      description: The template for omitting properties.
    CreateExternalApiRequest:
      type: object
      required:
        - name
        - urlPath
        - httpMethod
        - config
      properties:
        name:
          type: string
        baseUrl:
          type: string
        urlPath:
          type: string
        httpMethod:
          type: string
          enum:
            - get
            - post
            - put
            - delete
            - patch
            - options
            - head
            - connect
            - trace
        config:
          $ref: '#/components/schemas/ExternalApiRequestConfig'
      description: The template for omitting properties.
    CreateInternalTableDataSourceRequest:
      type: object
      required:
        - dataSourceType
        - name
      properties:
        dataSourceType:
          type: string
          enum:
            - internal_table
        config:
          $ref: '#/components/schemas/InternalTableConfig'
        name:
          type: string
        description:
          type: string
      description: The template for omitting properties.
    CreateMiniAppPageRequest:
      type: object
      required:
        - miniAppVersionId
        - name
        - isHidden
        - hideInNavbar
        - position
      properties:
        miniAppVersionId:
          type: integer
          format: int32
        name:
          type: string
        title:
          type: string
        icon:
          type: string
        isHidden:
          type: boolean
        hideInNavbar:
          type: boolean
        position:
          type: integer
          format: int32
        widgets:
          type: array
          items:
            anyOf:
              - $ref: '#/components/schemas/FormContainerWidget'
              - $ref: '#/components/schemas/CollectionWidget'
              - $ref: '#/components/schemas/SeparatorWidget'
              - $ref: '#/components/schemas/TitleWidget'
              - $ref: '#/components/schemas/TextWidget'
              - $ref: '#/components/schemas/RichTextWidget'
              - $ref: '#/components/schemas/AlertWidget'
              - $ref: '#/components/schemas/FieldsWidget'
              - $ref: '#/components/schemas/ImageWidget'
              - $ref: '#/components/schemas/VideoWidget'
              - $ref: '#/components/schemas/BigNumberWidget'
              - $ref: '#/components/schemas/ButtonWidget'
              - $ref: '#/components/schemas/LinkWidget'
              - $ref: '#/components/schemas/TextEntryWidget'
              - $ref: '#/components/schemas/DateTimeWidget'
              - $ref: '#/components/schemas/DateWidget'
              - $ref: '#/components/schemas/TimeWidget'
              - $ref: '#/components/schemas/NumberEntryWidget'
              - $ref: '#/components/schemas/PhoneEntryWidget'
              - $ref: '#/components/schemas/EmailEntryWidget'
              - $ref: '#/components/schemas/CheckboxWidget'
              - $ref: '#/components/schemas/ImagePickerWidget'
              - $ref: '#/components/schemas/FilePickerWidget'
              - $ref: '#/components/schemas/ChoiceWidget'
      description: The template for omitting properties.
    CreateMiniAppRequest:
      type: object
      required:
        - name
      properties:
        name:
          type: string
        description:
          type: string
        customerServiceContactNumber:
          type: string
        customerServiceContactEmail:
          type: string
        termsAndConditionsUrl:
          type: string
        categoryId:
          type: integer
          format: int32
      description: The template for omitting properties.
    CreateMiniAppVersionRequest:
      type: object
      required:
        - version
        - miniAppType
      properties:
        version:
          type: string
        miniAppType:
          $ref: '#/components/schemas/MiniAppType'
        releaseNote:
          type: string
        miniAppUrl:
          type: string
        url:
          type: string
        thumbnailUrl:
          type: string
      description: The template for omitting properties.
    CreateMiniAppWidgetRequest:
      type: object
      required:
        - name
        - position
        - isHidden
        - widgetType
        - config
      properties:
        name:
          type: string
        position:
          type: integer
          format: int32
        isHidden:
          type: boolean
        actionConfig:
          type: string
        bindingConfig:
          type: string
        widgetType:
          $ref: '#/components/schemas/WidgetType'
        config: {}
    DataSourceType:
      type: string
      enum:
        - internal_table
        - external_api
    DateTimeWidget:
      type: object
      required:
        - widgetType
      properties:
        widgetType:
          type: string
          enum:
            - date_time
      allOf:
        - $ref: '#/components/schemas/BaseInputWidget'
    DateTimeWidgetUpdate:
      type: object
      properties:
        widgetType:
          type: string
          enum:
            - date_time
      allOf:
        - $ref: '#/components/schemas/BaseInputWidgetUpdate'
    DateWidget:
      type: object
      required:
        - widgetType
      properties:
        widgetType:
          type: string
          enum:
            - date
      allOf:
        - $ref: '#/components/schemas/BaseInputWidget'
    DateWidgetUpdate:
      type: object
      properties:
        widgetType:
          type: string
          enum:
            - date
      allOf:
        - $ref: '#/components/schemas/BaseInputWidgetUpdate'
    EmailEntryWidget:
      type: object
      required:
        - widgetType
      properties:
        widgetType:
          type: string
          enum:
            - email_entry
      allOf:
        - $ref: '#/components/schemas/BaseInputWidget'
    EmailEntryWidgetUpdate:
      type: object
      properties:
        widgetType:
          type: string
          enum:
            - email_entry
      allOf:
        - $ref: '#/components/schemas/BaseInputWidgetUpdate'
    ExternalApiConfig:
      type: object
      required:
        - name
      properties:
        name:
          type: string
        baseUrl:
          type: string
      allOf:
        - $ref: '#/components/schemas/ExternalApiRequestConfig'
    ExternalApiConfigUpdate:
      type: object
      properties:
        name:
          type: string
        baseUrl:
          type: string
      allOf:
        - $ref: '#/components/schemas/ExternalApiRequestConfig'
    ExternalApiDataSource:
      type: object
      required:
        - dataSourceType
      properties:
        dataSourceType:
          type: string
          enum:
            - external_api
        config:
          $ref: '#/components/schemas/ExternalApiConfig'
      allOf:
        - $ref: '#/components/schemas/BaseDataSource'
    ExternalApiRequestConfig:
      type: object
      properties:
        routeParameters:
          type: object
          additionalProperties:
            type: string
        headers:
          type: object
          additionalProperties:
            type: string
        queryParameters:
          type: object
          additionalProperties:
            type: string
        body:
          type: object
          additionalProperties: {}
        cookies:
          type: object
          additionalProperties:
            type: string
    ExternalApiResponse:
      type: object
      required:
        - id
        - name
        - urlPath
        - httpMethod
        - config
      properties:
        id:
          type: integer
          format: int32
        name:
          type: string
        baseUrl:
          type: string
        urlPath:
          type: string
        httpMethod:
          type: string
          enum:
            - get
            - post
            - put
            - delete
            - patch
            - options
            - head
            - connect
            - trace
        config:
          $ref: '#/components/schemas/ExternalApiRequestConfig'
    ExternalApiResponseSnapshot:
      type: object
      required:
        - isSuccess
        - statusCode
        - data
      properties:
        isSuccess:
          type: boolean
        statusCode:
          type: integer
          format: int32
        data: {}
    FieldsWidget:
      type: object
      required:
        - widgetType
        - config
      properties:
        widgetType:
          type: string
          enum:
            - fields
        config:
          type: object
          properties:
            fields:
              type: array
              items:
                type: object
                properties:
                  title:
                    type: string
                  subtitle:
                    type: string
                  meta:
                    type: string
                  image:
                    type: string
                required:
                  - title
            design:
              type: object
              properties:
                style:
                  type: string
                  enum:
                    - default
                    - compact
                imageShape:
                  type: string
                  enum:
                    - circle
                    - square
              required:
                - style
                - imageShape
          required:
            - fields
            - design
      allOf:
        - $ref: '#/components/schemas/BaseWidget'
    FieldsWidgetUpdate:
      type: object
      properties:
        widgetType:
          type: string
          enum:
            - fields
        config:
          type: object
          properties:
            fields:
              type: array
              items:
                type: object
                properties:
                  title:
                    type: string
                  subtitle:
                    type: string
                  meta:
                    type: string
                  image:
                    type: string
                required:
                  - title
            design:
              type: object
              properties:
                style:
                  type: string
                  enum:
                    - default
                    - compact
                imageShape:
                  type: string
                  enum:
                    - circle
                    - square
      allOf:
        - $ref: '#/components/schemas/BaseWidgetUpdate'
    FilePickerWidget:
      type: object
      required:
        - widgetType
      properties:
        widgetType:
          type: string
          enum:
            - file_picker
      allOf:
        - $ref: '#/components/schemas/BaseInputWidget'
    FilePickerWidgetUpdate:
      type: object
      properties:
        widgetType:
          type: string
          enum:
            - file_picker
      allOf:
        - $ref: '#/components/schemas/BaseInputWidgetUpdate'
    FormContainerWidget:
      type: object
      required:
        - widgetType
        - config
      properties:
        widgetType:
          type: string
          enum:
            - form_container
        config:
          type: object
          properties:
            title:
              type: string
            subtitle:
              type: string
            bindingConfig:
              $ref: '#/components/schemas/BindingConfig'
            widgets:
              type: array
              items:
                anyOf:
                  - $ref: '#/components/schemas/CollectionWidget'
                  - $ref: '#/components/schemas/SeparatorWidget'
                  - $ref: '#/components/schemas/TitleWidget'
                  - $ref: '#/components/schemas/TextWidget'
                  - $ref: '#/components/schemas/RichTextWidget'
                  - $ref: '#/components/schemas/AlertWidget'
                  - $ref: '#/components/schemas/FieldsWidget'
                  - $ref: '#/components/schemas/ImageWidget'
                  - $ref: '#/components/schemas/VideoWidget'
                  - $ref: '#/components/schemas/BigNumberWidget'
                  - $ref: '#/components/schemas/ButtonWidget'
                  - $ref: '#/components/schemas/LinkWidget'
                  - $ref: '#/components/schemas/TextEntryWidget'
                  - $ref: '#/components/schemas/DateTimeWidget'
                  - $ref: '#/components/schemas/DateWidget'
                  - $ref: '#/components/schemas/TimeWidget'
                  - $ref: '#/components/schemas/NumberEntryWidget'
                  - $ref: '#/components/schemas/PhoneEntryWidget'
                  - $ref: '#/components/schemas/EmailEntryWidget'
                  - $ref: '#/components/schemas/CheckboxWidget'
                  - $ref: '#/components/schemas/ImagePickerWidget'
                  - $ref: '#/components/schemas/FilePickerWidget'
                  - $ref: '#/components/schemas/ChoiceWidget'
            design:
              type: object
              properties:
                submitButtonText:
                  type: string
          required:
            - widgets
      allOf:
        - $ref: '#/components/schemas/BaseWidget'
    FormContainerWidgetUpdate:
      type: object
      properties:
        widgetType:
          type: string
          enum:
            - form_container
        config:
          type: object
          properties:
            title:
              type: string
            subtitle:
              type: string
            bindingConfig:
              $ref: '#/components/schemas/BindingConfigUpdate'
            widgets:
              type: array
              items:
                anyOf:
                  - $ref: '#/components/schemas/CollectionWidget'
                  - $ref: '#/components/schemas/SeparatorWidget'
                  - $ref: '#/components/schemas/TitleWidget'
                  - $ref: '#/components/schemas/TextWidget'
                  - $ref: '#/components/schemas/RichTextWidget'
                  - $ref: '#/components/schemas/AlertWidget'
                  - $ref: '#/components/schemas/FieldsWidget'
                  - $ref: '#/components/schemas/ImageWidget'
                  - $ref: '#/components/schemas/VideoWidget'
                  - $ref: '#/components/schemas/BigNumberWidget'
                  - $ref: '#/components/schemas/ButtonWidget'
                  - $ref: '#/components/schemas/LinkWidget'
                  - $ref: '#/components/schemas/TextEntryWidget'
                  - $ref: '#/components/schemas/DateTimeWidget'
                  - $ref: '#/components/schemas/DateWidget'
                  - $ref: '#/components/schemas/TimeWidget'
                  - $ref: '#/components/schemas/NumberEntryWidget'
                  - $ref: '#/components/schemas/PhoneEntryWidget'
                  - $ref: '#/components/schemas/EmailEntryWidget'
                  - $ref: '#/components/schemas/CheckboxWidget'
                  - $ref: '#/components/schemas/ImagePickerWidget'
                  - $ref: '#/components/schemas/FilePickerWidget'
                  - $ref: '#/components/schemas/ChoiceWidget'
            design:
              type: object
              properties:
                submitButtonText:
                  type: string
      allOf:
        - $ref: '#/components/schemas/BaseWidgetUpdate'
    GenericError:
      type: object
      required:
        - type
        - statusCode
        - message
      properties:
        type:
          type: string
        statusCode:
          type: number
        message:
          type: string
          default: An error occurred
    ImagePickerWidget:
      type: object
      required:
        - widgetType
      properties:
        widgetType:
          type: string
          enum:
            - image_picker
      allOf:
        - $ref: '#/components/schemas/BaseInputWidget'
    ImagePickerWidgetUpdate:
      type: object
      properties:
        widgetType:
          type: string
          enum:
            - image_picker
      allOf:
        - $ref: '#/components/schemas/BaseInputWidgetUpdate'
    ImageWidget:
      type: object
      required:
        - widgetType
        - config
      properties:
        widgetType:
          type: string
          enum:
            - image
        config:
          type: object
          properties:
            content:
              type: string
            design:
              type: object
              properties:
                aspectRatio:
                  type: string
                  enum:
                    - '1:1'
                    - '16:9'
                    - '4:3'
                    - '3:2'
                    - auto
                fill:
                  type: string
                  enum:
                    - fill
                    - contain
                fullWidth:
                  type: boolean
              required:
                - aspectRatio
                - fill
          required:
            - content
            - design
      allOf:
        - $ref: '#/components/schemas/BaseWidget'
    ImageWidgetUpdate:
      type: object
      properties:
        widgetType:
          type: string
          enum:
            - image
        config:
          type: object
          properties:
            content:
              type: string
            design:
              type: object
              properties:
                aspectRatio:
                  type: string
                  enum:
                    - '1:1'
                    - '16:9'
                    - '4:3'
                    - '3:2'
                    - auto
                fill:
                  type: string
                  enum:
                    - fill
                    - contain
                fullWidth:
                  type: boolean
      allOf:
        - $ref: '#/components/schemas/BaseWidgetUpdate'
    InternalTableConfig:
      type: object
      required:
        - permissions
        - schema
      properties:
        permissions:
          $ref: '#/components/schemas/InternalTablePermissions'
        schema:
          type: array
          items:
            $ref: '#/components/schemas/InternalTableSchema'
    InternalTableConfigUpdate:
      type: object
      properties:
        permissions:
          $ref: '#/components/schemas/InternalTablePermissionsUpdate'
        schema:
          type: array
          items:
            $ref: '#/components/schemas/InternalTableSchema'
    InternalTableDataSource:
      type: object
      required:
        - dataSourceType
      properties:
        dataSourceType:
          type: string
          enum:
            - internal_table
        config:
          $ref: '#/components/schemas/InternalTableConfig'
      allOf:
        - $ref: '#/components/schemas/BaseDataSource'
    InternalTableFieldType:
      type: string
      enum:
        - string
        - number
        - boolean
        - datetime
        - image
    InternalTablePermissionType:
      type: string
      enum:
        - allow
        - deny
        - owner
    InternalTablePermissions:
      type: object
      required:
        - create
        - read
        - update
        - delete
      properties:
        create:
          $ref: '#/components/schemas/InternalTablePermissionType'
        read:
          $ref: '#/components/schemas/InternalTablePermissionType'
        update:
          $ref: '#/components/schemas/InternalTablePermissionType'
        delete:
          $ref: '#/components/schemas/InternalTablePermissionType'
    InternalTablePermissionsUpdate:
      type: object
      properties:
        create:
          $ref: '#/components/schemas/InternalTablePermissionType'
        read:
          $ref: '#/components/schemas/InternalTablePermissionType'
        update:
          $ref: '#/components/schemas/InternalTablePermissionType'
        delete:
          $ref: '#/components/schemas/InternalTablePermissionType'
    InternalTableSchema:
      type: object
      required:
        - name
        - type
        - required
      properties:
        name:
          type: string
        type:
          $ref: '#/components/schemas/InternalTableFieldType'
        required:
          type: boolean
    InvitationResponse:
      type: object
      required:
        - id
        - email
        - invitationStatus
        - token
        - invitedBy
        - expiredAt
        - createdAt
      properties:
        id:
          type: integer
          format: int64
        email:
          type: string
        invitationStatus:
          $ref: '#/components/schemas/InvitationStatus'
        systemRoles:
          type: array
          items:
            $ref: '#/components/schemas/SystemRole'
        teamAssignments:
          type: array
          items:
            type: object
            properties:
              teamId:
                type: integer
                format: int32
              teamName:
                type: string
              teamRoles:
                type: array
                items:
                  $ref: '#/components/schemas/TeamRole'
            required:
              - teamId
              - teamName
              - teamRoles
        token:
          type: string
        invitedBy:
          type: object
          properties:
            id:
              type: integer
              format: int32
            name:
              type: string
            email:
              type: string
          required:
            - id
            - name
            - email
        expiredAt:
          type: string
        createdAt:
          type: string
    InvitationStatus:
      type: string
      enum:
        - pending
        - accepted
        - cancelled
    InviteToSystemRequest:
      type: object
      required:
        - email
        - systemRoles
      properties:
        email:
          type: string
        systemRoles:
          type: array
          items:
            type: string
    InviteToTeamRequest:
      type: object
      required:
        - email
        - roles
      properties:
        email:
          type: string
        roles:
          type: array
          items:
            type: string
    LinkWidget:
      type: object
      required:
        - widgetType
        - config
      properties:
        widgetType:
          type: string
          enum:
            - link
        config:
          type: object
          properties:
            links:
              type: array
              items:
                $ref: '#/components/schemas/Action'
            design:
              type: object
              properties:
                size:
                  type: string
                  enum:
                    - left
                    - none
                    - right
          required:
            - links
      allOf:
        - $ref: '#/components/schemas/BaseWidget'
    LinkWidgetUpdate:
      type: object
      properties:
        widgetType:
          type: string
          enum:
            - link
        config:
          type: object
          properties:
            links:
              type: array
              items:
                $ref: '#/components/schemas/Action'
            design:
              type: object
              properties:
                size:
                  type: string
                  enum:
                    - left
                    - none
                    - right
      allOf:
        - $ref: '#/components/schemas/BaseWidgetUpdate'
    MiniApp:
      type: object
      required:
        - id
        - name
        - miniAppStatus
        - teamId
        - createdAt
        - createdBy
      properties:
        id:
          type: integer
          format: int32
        name:
          type: string
        description:
          type: string
        customerServiceContactNumber:
          type: string
        customerServiceContactEmail:
          type: string
        termsAndConditionsUrl:
          type: string
        miniAppStatus:
          $ref: '#/components/schemas/MiniAppStatus'
        categoryId:
          type: integer
          format: int32
        teamId:
          type: integer
          format: int32
        createdAt:
          type: string
        createdBy:
          type: string
        updatedAt:
          type: string
        updatedBy:
          type: string
    MiniAppPage:
      type: object
      required:
        - id
        - miniAppVersionId
        - name
        - isHidden
        - hideInNavbar
        - position
      properties:
        id:
          type: integer
          format: int32
        miniAppVersionId:
          type: integer
          format: int32
        name:
          type: string
        title:
          type: string
        icon:
          type: string
        isHidden:
          type: boolean
        hideInNavbar:
          type: boolean
        position:
          type: integer
          format: int32
        widgets:
          type: array
          items:
            anyOf:
              - $ref: '#/components/schemas/FormContainerWidget'
              - $ref: '#/components/schemas/CollectionWidget'
              - $ref: '#/components/schemas/SeparatorWidget'
              - $ref: '#/components/schemas/TitleWidget'
              - $ref: '#/components/schemas/TextWidget'
              - $ref: '#/components/schemas/RichTextWidget'
              - $ref: '#/components/schemas/AlertWidget'
              - $ref: '#/components/schemas/FieldsWidget'
              - $ref: '#/components/schemas/ImageWidget'
              - $ref: '#/components/schemas/VideoWidget'
              - $ref: '#/components/schemas/BigNumberWidget'
              - $ref: '#/components/schemas/ButtonWidget'
              - $ref: '#/components/schemas/LinkWidget'
              - $ref: '#/components/schemas/TextEntryWidget'
              - $ref: '#/components/schemas/DateTimeWidget'
              - $ref: '#/components/schemas/DateWidget'
              - $ref: '#/components/schemas/TimeWidget'
              - $ref: '#/components/schemas/NumberEntryWidget'
              - $ref: '#/components/schemas/PhoneEntryWidget'
              - $ref: '#/components/schemas/EmailEntryWidget'
              - $ref: '#/components/schemas/CheckboxWidget'
              - $ref: '#/components/schemas/ImagePickerWidget'
              - $ref: '#/components/schemas/FilePickerWidget'
              - $ref: '#/components/schemas/ChoiceWidget'
    MiniAppStatus:
      type: string
      enum:
        - removed_by_admin
        - active
    MiniAppType:
      type: string
      enum:
        - url
        - bundle
        - builder
    MiniAppVersion:
      type: object
      required:
        - id
        - version
        - miniAppType
        - miniAppVersionStatus
        - createdAt
        - createdBy
      properties:
        id:
          type: integer
          format: int32
        version:
          type: string
        miniAppType:
          $ref: '#/components/schemas/MiniAppType'
        miniAppVersionStatus:
          $ref: '#/components/schemas/MiniAppVersionStatus'
        releaseNote:
          type: string
        miniAppUrl:
          type: string
        url:
          type: string
        thumbnailUrl:
          type: string
        s3Key:
          type: string
        createdAt:
          type: string
        createdBy:
          type: string
        updatedAt:
          type: string
        updatedBy:
          type: string
    MiniAppVersionDetail:
      type: object
      required:
        - pages
      properties:
        pages:
          type: array
          items:
            $ref: '#/components/schemas/MiniAppPage'
      allOf:
        - $ref: '#/components/schemas/MiniAppVersion'
    MiniAppVersionStatus:
      type: string
      enum:
        - draft
        - in_review
        - approved
        - live
        - rejected
    MoveRequest:
      type: object
      properties:
        targetPosition:
          type: integer
          format: int32
    NumberEntryWidget:
      type: object
      required:
        - widgetType
      properties:
        widgetType:
          type: string
          enum:
            - number_entry
      allOf:
        - $ref: '#/components/schemas/BaseInputWidget'
    NumberEntryWidgetUpdate:
      type: object
      properties:
        widgetType:
          type: string
          enum:
            - number_entry
      allOf:
        - $ref: '#/components/schemas/BaseInputWidgetUpdate'
    PhoneEntryWidget:
      type: object
      required:
        - widgetType
      properties:
        widgetType:
          type: string
          enum:
            - phone_entry
      allOf:
        - $ref: '#/components/schemas/BaseInputWidget'
    PhoneEntryWidgetUpdate:
      type: object
      properties:
        widgetType:
          type: string
          enum:
            - phone_entry
      allOf:
        - $ref: '#/components/schemas/BaseInputWidgetUpdate'
    RefreshTokenRequest:
      type: object
      required:
        - userId
        - refreshToken
      properties:
        userId:
          type: string
        refreshToken:
          type: string
    RefreshTokenResponse:
      type: object
      required:
        - refreshToken
        - accessToken
      properties:
        refreshToken:
          type: string
        accessToken:
          type: string
    RichTextWidget:
      type: object
      required:
        - widgetType
        - config
      properties:
        widgetType:
          type: string
          enum:
            - rich_text
        config:
          type: object
          properties:
            content:
              type: string
          required:
            - content
      allOf:
        - $ref: '#/components/schemas/BaseWidget'
    RichTextWidgetUpdate:
      type: object
      properties:
        widgetType:
          type: string
          enum:
            - rich_text
        config:
          type: object
          properties:
            content:
              type: string
      allOf:
        - $ref: '#/components/schemas/BaseWidgetUpdate'
    SeparatorWidget:
      type: object
      required:
        - widgetType
        - config
      properties:
        widgetType:
          type: string
          enum:
            - separator
        config:
          type: object
          properties:
            design:
              type: object
              properties:
                size:
                  type: string
                  enum:
                    - small
                    - medium
                    - large
                drawLine:
                  type: boolean
              required:
                - size
          required:
            - design
      allOf:
        - $ref: '#/components/schemas/BaseWidget'
    SeparatorWidgetUpdate:
      type: object
      properties:
        widgetType:
          type: string
          enum:
            - separator
        config:
          type: object
          properties:
            design:
              type: object
              properties:
                size:
                  type: string
                  enum:
                    - small
                    - medium
                    - large
                drawLine:
                  type: boolean
      allOf:
        - $ref: '#/components/schemas/BaseWidgetUpdate'
    SystemRole:
      type: string
      enum:
        - system_admin
        - system_editor
    TeamRole:
      type: string
      enum:
        - team_admin
        - team_developer
    TeamUserResponse:
      type: object
      properties:
        teamRoles:
          type: array
          items:
            $ref: '#/components/schemas/TeamRole'
      allOf:
        - $ref: '#/components/schemas/BaseUser'
    TeamUserResponseUpdate:
      type: object
      properties:
        teamRoles:
          type: array
          items:
            $ref: '#/components/schemas/TeamRole'
      allOf:
        - $ref: '#/components/schemas/BaseUserUpdate'
    TextEntryWidget:
      type: object
      required:
        - widgetType
      properties:
        widgetType:
          type: string
          enum:
            - text_entry
      allOf:
        - $ref: '#/components/schemas/BaseInputWidget'
    TextEntryWidgetUpdate:
      type: object
      properties:
        widgetType:
          type: string
          enum:
            - text_entry
      allOf:
        - $ref: '#/components/schemas/BaseInputWidgetUpdate'
    TextWidget:
      type: object
      required:
        - widgetType
        - config
      properties:
        widgetType:
          type: string
          enum:
            - text
        config:
          type: object
          properties:
            content:
              type: string
            design:
              type: object
              properties:
                style:
                  type: string
                  enum:
                    - large
                    - regular
                    - small
                    - footnote
                    - meta_text
                    - headline_xsmall
                    - headline_small
                    - headline_medium
                    - headline_large
                    - headline_xlarge
                textAlign:
                  type: string
                  enum:
                    - left
                    - center
                    - right
              required:
                - style
                - textAlign
          required:
            - content
            - design
      allOf:
        - $ref: '#/components/schemas/BaseWidget'
    TextWidgetUpdate:
      type: object
      properties:
        widgetType:
          type: string
          enum:
            - text
        config:
          type: object
          properties:
            content:
              type: string
            design:
              type: object
              properties:
                style:
                  type: string
                  enum:
                    - large
                    - regular
                    - small
                    - footnote
                    - meta_text
                    - headline_xsmall
                    - headline_small
                    - headline_medium
                    - headline_large
                    - headline_xlarge
                textAlign:
                  type: string
                  enum:
                    - left
                    - center
                    - right
      allOf:
        - $ref: '#/components/schemas/BaseWidgetUpdate'
    TimeWidget:
      type: object
      required:
        - widgetType
      properties:
        widgetType:
          type: string
          enum:
            - time
      allOf:
        - $ref: '#/components/schemas/BaseInputWidget'
    TimeWidgetUpdate:
      type: object
      properties:
        widgetType:
          type: string
          enum:
            - time
      allOf:
        - $ref: '#/components/schemas/BaseInputWidgetUpdate'
    TitleWidget:
      type: object
      required:
        - widgetType
        - config
      properties:
        widgetType:
          type: string
          enum:
            - title
        config:
          type: object
          properties:
            style:
              type: string
              enum:
                - simple
                - banner
            data:
              type: object
              properties:
                title:
                  type: string
                subtitle:
                  type: string
                meta:
                  type: string
                image:
                  type: string
            design:
              type: object
              properties:
                imageFill:
                  type: string
                  enum:
                    - fill
                    - contain
              required:
                - imageFill
          required:
            - style
            - data
            - design
      allOf:
        - $ref: '#/components/schemas/BaseWidget'
    TitleWidgetUpdate:
      type: object
      properties:
        widgetType:
          type: string
          enum:
            - title
        config:
          type: object
          properties:
            style:
              type: string
              enum:
                - simple
                - banner
            data:
              type: object
              properties:
                title:
                  type: string
                subtitle:
                  type: string
                meta:
                  type: string
                image:
                  type: string
            design:
              type: object
              properties:
                imageFill:
                  type: string
                  enum:
                    - fill
                    - contain
      allOf:
        - $ref: '#/components/schemas/BaseWidgetUpdate'
    UpdateExternalApiDataSourceRequest:
      type: object
      properties:
        dataSourceType:
          type: string
          enum:
            - external_api
        config:
          $ref: '#/components/schemas/ExternalApiConfigUpdate'
        name:
          type: string
        description:
          type: string
      description: The template for omitting properties.
    UpdateExternalApiRequest:
      type: object
      properties:
        name:
          type: string
        baseUrl:
          type: string
        urlPath:
          type: string
        httpMethod:
          type: string
          enum:
            - get
            - post
            - put
            - delete
            - patch
            - options
            - head
            - connect
            - trace
        config:
          $ref: '#/components/schemas/ExternalApiRequestConfig'
      description: The template for omitting properties.
    UpdateInternalTableDataSourceRequest:
      type: object
      properties:
        dataSourceType:
          type: string
          enum:
            - internal_table
        config:
          $ref: '#/components/schemas/InternalTableConfigUpdate'
        name:
          type: string
        description:
          type: string
      description: The template for omitting properties.
    UpdateMiniAppPageRequest:
      type: object
      properties:
        name:
          type: string
        title:
          type: string
        icon:
          type: string
        isHidden:
          type: boolean
        hideInNavbar:
          type: boolean
        position:
          type: integer
          format: int32
        widgets:
          type: array
          items:
            anyOf:
              - $ref: '#/components/schemas/FormContainerWidget'
              - $ref: '#/components/schemas/CollectionWidget'
              - $ref: '#/components/schemas/SeparatorWidget'
              - $ref: '#/components/schemas/TitleWidget'
              - $ref: '#/components/schemas/TextWidget'
              - $ref: '#/components/schemas/RichTextWidget'
              - $ref: '#/components/schemas/AlertWidget'
              - $ref: '#/components/schemas/FieldsWidget'
              - $ref: '#/components/schemas/ImageWidget'
              - $ref: '#/components/schemas/VideoWidget'
              - $ref: '#/components/schemas/BigNumberWidget'
              - $ref: '#/components/schemas/ButtonWidget'
              - $ref: '#/components/schemas/LinkWidget'
              - $ref: '#/components/schemas/TextEntryWidget'
              - $ref: '#/components/schemas/DateTimeWidget'
              - $ref: '#/components/schemas/DateWidget'
              - $ref: '#/components/schemas/TimeWidget'
              - $ref: '#/components/schemas/NumberEntryWidget'
              - $ref: '#/components/schemas/PhoneEntryWidget'
              - $ref: '#/components/schemas/EmailEntryWidget'
              - $ref: '#/components/schemas/CheckboxWidget'
              - $ref: '#/components/schemas/ImagePickerWidget'
              - $ref: '#/components/schemas/FilePickerWidget'
              - $ref: '#/components/schemas/ChoiceWidget'
      description: The template for omitting properties.
    UpdateMiniAppRequest:
      type: object
      properties:
        name:
          type: string
        description:
          type: string
        customerServiceContactNumber:
          type: string
        customerServiceContactEmail:
          type: string
        termsAndConditionsUrl:
          type: string
        miniAppStatus:
          $ref: '#/components/schemas/MiniAppStatus'
        categoryId:
          type: integer
          format: int32
      description: The template for omitting properties.
    UpdateMiniAppVersionRequest:
      type: object
      properties:
        version:
          type: string
        miniAppType:
          $ref: '#/components/schemas/MiniAppType'
        releaseNote:
          type: string
        miniAppUrl:
          type: string
        url:
          type: string
        thumbnailUrl:
          type: string
      description: The template for omitting properties.
    UpdateUserRequest:
      type: object
      properties:
        systemRoles:
          type: array
          items:
            $ref: '#/components/schemas/SystemRole'
        teams:
          type: array
          items:
            $ref: '#/components/schemas/UserTeamRoles'
        createdAt:
          type: string
          format: date-time
        email:
          type: string
        name:
          type: string
        userStatus:
          $ref: '#/components/schemas/UserStatus'
      description: The template for omitting properties.
    UserLoginRequest:
      type: object
      required:
        - email
        - password
      properties:
        email:
          type: string
        password:
          type: string
    UserLoginResponse:
      type: object
      required:
        - user
        - refreshToken
        - accessToken
      properties:
        user:
          $ref: '#/components/schemas/UserResponse'
        refreshToken:
          type: string
        accessToken:
          type: string
    UserResponse:
      type: object
      required:
        - createdAt
      properties:
        systemRoles:
          type: array
          items:
            $ref: '#/components/schemas/SystemRole'
        teams:
          type: array
          items:
            $ref: '#/components/schemas/UserTeamRoles'
        createdAt:
          type: string
          format: date-time
      allOf:
        - $ref: '#/components/schemas/BaseUser'
    UserResponseUpdate:
      type: object
      properties:
        systemRoles:
          type: array
          items:
            $ref: '#/components/schemas/SystemRole'
        teams:
          type: array
          items:
            $ref: '#/components/schemas/UserTeamRoles'
        createdAt:
          type: string
          format: date-time
      allOf:
        - $ref: '#/components/schemas/BaseUserUpdate'
    UserStatus:
      type: string
      enum:
        - active
        - inactive
        - suspended
        - deleted
    UserTeamRoles:
      type: object
      required:
        - id
        - name
        - roles
      properties:
        id:
          type: integer
          format: int32
        name:
          type: string
        roles:
          type: array
          items:
            $ref: '#/components/schemas/TeamRole'
    ValidationError:
      type: object
      required:
        - type
        - statusCode
        - message
        - errors
      properties:
        type:
          type: string
        statusCode:
          type: number
        message:
          type: string
          default: An error occurred
        errors: {}
    VideoWidget:
      type: object
      required:
        - widgetType
        - config
      properties:
        widgetType:
          type: string
          enum:
            - video
        config:
          type: object
          properties:
            url:
              type: string
            design:
              type: object
              properties:
                aspectRatio:
                  type: string
                  enum:
                    - '1:1'
                    - '16:9'
                    - '4:3'
                    - '3:2'
                fullWidth:
                  type: boolean
              required:
                - aspectRatio
          required:
            - url
      allOf:
        - $ref: '#/components/schemas/BaseWidget'
    VideoWidgetUpdate:
      type: object
      properties:
        widgetType:
          type: string
          enum:
            - video
        config:
          type: object
          properties:
            url:
              type: string
            design:
              type: object
              properties:
                aspectRatio:
                  type: string
                  enum:
                    - '1:1'
                    - '16:9'
                    - '4:3'
                    - '3:2'
                fullWidth:
                  type: boolean
      allOf:
        - $ref: '#/components/schemas/BaseWidgetUpdate'
    WidgetType:
      type: string
      enum:
        - collection
        - title
        - separator
        - text
        - rich_text
        - alert
        - fields
        - image
        - video
        - big_number
        - button
        - link
        - form_container
        - text_entry
        - date_time
        - date
        - time
        - number_entry
        - phone_entry
        - email_entry
        - checkbox
        - image_picker
        - file_picker
        - choice
servers:
  - url: https://6pl6otxn2kp5e2te2hollaczb40pthsl.lambda-url.ap-southeast-1.on.aws
    variables: {}
