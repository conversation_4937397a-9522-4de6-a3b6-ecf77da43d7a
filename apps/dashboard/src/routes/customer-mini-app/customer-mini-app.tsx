import { renderWidget } from "@/components/widgets"
import { cn } from "@/lib/utils"
import { components } from "@saf/sdk"
import { DropdownMenu, Heading, IconButton, Text } from "@saf/ui"
import { IconMenu2 } from "@tabler/icons-react"
import { useEffect, useState } from "react"
import { Page } from "../mini-apps/mini-app-editor-design/stores/mini-app-design-store"

// This component should call mini app API for for customer and render CustomerMiniAppRoot
export const CustomerMiniApp = () => {
  // return <CustomerMiniAppRoot data={} />;
  // const { teamId } = useParams();
}

export const CustomerMiniAppRoot = ({
  data,
  focusedPageId,
  onPageChanged,
  focusedWidgetId,
  onWidgetChanged,
}: {
  data: components["schemas"]["MiniAppVersionDetail"]
  focusedPageId?: number
  onPageChanged?: (pageId: number) => void
  focusedWidgetId?: number
  onWidgetChanged?: (widgetId: number) => void
}) => {
  const [_selectedPageId, setSelectedPageId] = useState<number | undefined>(focusedPageId)
  const selectedPage: Page | undefined = data.pages?.length
    ? (data.pages.find((page) => page.id === _selectedPageId) ?? data.pages[0])
    : undefined

  const handlePageChange = (pageId: number) => {
    setSelectedPageId(pageId)
    if (onPageChanged) {
      onPageChanged(pageId)
    }
  }

  useEffect(() => {
    if (focusedPageId != _selectedPageId) {
      setSelectedPageId(focusedPageId)
    }
  }, [_selectedPageId, focusedPageId])

  const filteredPages = data.pages.filter((page) => !page.isHidden && !page.hideInNavbar)

  const maxVisiblePages = 5
  const hasMorePages = filteredPages.length > maxVisiblePages

  const pagesToShow = hasMorePages ? filteredPages.slice(0, maxVisiblePages - 1) : filteredPages

  const remainingPages = hasMorePages ? filteredPages.slice(maxVisiblePages - 1) : []

  return (
    <div className="flex size-full flex-col bg-ui-bg-subtle">
      <header className="flex items-center gap-3 border-b bg-ui-bg-base px-2 pb-2 pt-3">
        <IconButton variant="transparent">
          <IconMenu2 />
        </IconButton>
        <Heading level="h1">{selectedPage?.title?.trim() || selectedPage?.name?.trim()}</Heading>
      </header>
      <main className="flex-1 overflow-y-auto">
        {selectedPage?.widgets
          ?.filter((widget) => !widget.isHidden)
          .map((widget) => renderWidget(widget, focusedWidgetId, onWidgetChanged))}
      </main>
      <nav className="flex w-full divide-x overflow-clip border-t">
        {pagesToShow.map((page) => (
          <button
            key={page.id}
            onClick={() => handlePageChange(page.id)}
            className="flex flex-1 flex-col items-center justify-center gap-1.5 bg-ui-bg-base px-2 pb-1.5 pt-2.5 text-center"
          >
            <div
              className={cn(
                "size-5 rounded border",
                page.id === _selectedPageId ? "bg-ui-button-inverted" : "bg-ui-bg-subtle",
              )}
            ></div>
            <Text
              size="small"
              className={cn(
                "max-w-20 truncate text-xs",
                page.id === _selectedPageId ? "text-ui-fg" : "text-ui-fg-muted",
              )}
              color="subtle"
              weight={page.id === _selectedPageId ? "plus" : "regular"}
            >
              {page.title?.trim() || page.name?.trim()}
            </Text>
          </button>
        ))}
        {remainingPages.length > 0 && <MoreNavItem pages={remainingPages} onPageChanged={handlePageChange} />}
      </nav>
    </div>
  )
}

export const MoreNavItem = ({
  pages,
  onPageChanged,
}: {
  pages: components["schemas"]["MiniAppVersionDetail"]["pages"]
  onPageChanged: (pageId: number) => void
}) => {
  return (
    <DropdownMenu>
      <DropdownMenu.Trigger asChild>
        <button className="flex flex-1 flex-col items-center justify-center bg-ui-bg-base p-1 text-center">
          <IconMenu2 className="size-5" />
        </button>
      </DropdownMenu.Trigger>
      <DropdownMenu.Content align="end" className="max-w-56">
        {pages.map((page) => (
          <DropdownMenu.Item
            key={page.id}
            onClick={() => {
              onPageChanged(page.id)
            }}
            className="truncate"
          >
            {page.title?.trim() || page.name?.trim()}
          </DropdownMenu.Item>
        ))}
      </DropdownMenu.Content>
    </DropdownMenu>
  )
}
