import { Field } from "@/components/common/field"
import { Form } from "@/components/common/form"
import { Checkbox, Input, Text } from "@saf/ui"
import { useFormContext } from "react-hook-form"
import { z } from "zod"

export const generalWidgetEditorSchema = z.object({
  name: z.string().optional(),
  isHidden: z.boolean(),
})

export type GeneralWidgetEditorSchema = z.infer<typeof generalWidgetEditorSchema>

export const GeneralWidgetFields = () => {
  const form = useFormContext<GeneralWidgetEditorSchema>()

  return (
    <div className="space-y-3 p-4">
      <Text className="uppercase" size="small">
        General
      </Text>
      <Field {...form} name="name" label="Name">
        <Input />
      </Field>
      <Form.Field
        control={form.control}
        name="isHidden"
        render={({ field: { value, onChange, ...field } }) => {
          return (
            <Form.Item className="flex flex-row items-center gap-3">
              <Form.Control>
                <Checkbox checked={value} onCheckedChange={onChange} {...field} />
              </Form.Control>
              <Form.Label>Hide Widget</Form.Label>
              <Form.ErrorMessage />
            </Form.Item>
          )
        }}
      />
    </div>
  )
}
