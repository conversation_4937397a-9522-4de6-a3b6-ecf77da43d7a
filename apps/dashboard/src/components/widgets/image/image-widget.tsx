import { components } from "@saf/sdk"

export const ImageWidget = ({ data }: { data: components["schemas"]["ImageWidget"] }) => {
  const { content, design } = data.config

  return (
    <div
      className={`image-widget ${design?.fullWidth ? "w-full" : "max-w-md"}`}
      style={{
        aspectRatio: design?.aspectRatio,
        objectFit: design?.fill === "fill" ? "cover" : "contain",
      }}
    >
      <img src={content} alt={data.name} />
    </div>
  )
}
