import { cn } from "@/lib/utils"
import { components } from "@saf/sdk"
import { cva, type VariantProps } from "class-variance-authority"

const textVariants = cva("", {
  variants: {
    style: {
      large: "text-lg font-normal",
      regular: "text-base font-normal",
      small: "text-sm font-normal",
      footnote: "text-xs font-normal",
      meta_text: "text-xs font-medium text-muted-foreground",
      headline_xsmall: "text-sm font-semibold",
      headline_small: "text-base font-semibold",
      headline_medium: "text-lg font-semibold",
      headline_large: "text-xl font-semibold",
      headline_xlarge: "text-2xl font-semibold",
    },
    textAlign: {
      left: "text-left",
      center: "text-center",
      right: "text-right",
    },
  },
  defaultVariants: {
    style: "regular",
    textAlign: "left",
  },
})

export interface TextWidgetProps extends VariantProps<typeof textVariants> {
  content: string
}

export const TextWidget = ({ data }: { data: components["schemas"]["TextWidget"] }) => {
  return (
    <div className="px-4 py-0.5">
      <p
        className={cn(
          textVariants({
            style: data.config.design?.style,
            textAlign: data.config.design?.textAlign,
          }),
        )}
      >
        {data.config.content}
      </p>
    </div>
  )
}
