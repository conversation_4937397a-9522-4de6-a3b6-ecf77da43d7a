import { cn } from "@/lib/utils"
import { components } from "@saf/sdk"
import { cva, type VariantProps } from "class-variance-authority"

const titleVariants = cva("", {
  variants: {
    style: {
      Simple: "flex flex-col gap-1",
      Banner: "flex items-center gap-4 rounded-lg bg-muted p-6",
    },
    imageFill: {
      fill: "object-cover",
      contain: "object-contain",
    },
  },
  defaultVariants: {
    style: "Simple",
    imageFill: "contain",
  },
})

export interface TitleWidgetProps extends VariantProps<typeof titleVariants> {
  data?: {
    title?: string
    subtitle?: string
    meta?: string
    image?: string
  }
  actions?: any[]
}

export const TitleWidget = ({ data }: { data: components["schemas"]["TitleWidget"] }) => {
  const { style = "Simple", design, data: titleData } = data
  const { title, subtitle, meta, image } = titleData || {}
  const { imageFill = "contain" } = design || {}

  return (
    <div
      className={cn(
        "p-4",
        titleVariants({
          style,
          imageFill,
        }),
      )}
    >
      {image && (
        <div className="relative h-12 w-12 overflow-hidden rounded-md">
          <img
            src={image}
            alt={title || "Title image"}
            className={cn("rounded-md", imageFill === "fill" ? "object-cover" : "object-contain")}
          />
        </div>
      )}
      <div className="flex flex-col gap-2">
        {!meta && <p className="text-muted-foreground mt-1 text-xs uppercase">{meta}</p>}
        {title && <h3 className="text-lg font-semibold leading-none tracking-tight">{title}</h3>}
        {subtitle && <h4 className="text-muted-foreground text-sm">{subtitle}</h4>}
      </div>
    </div>
  )
}
