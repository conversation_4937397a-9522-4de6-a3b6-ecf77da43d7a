import { cn } from "@/lib/utils"
import { components } from "@saf/sdk"
import { cva, type VariantProps } from "class-variance-authority"

const titleVariants = cva("", {
  variants: {
    style: {
      Simple: "flex items-start gap-3",
      Banner: "flex items-center gap-4 rounded-lg bg-ui-bg-subtle p-6",
    },
  },
  defaultVariants: {
    style: "Simple",
  },
})

const imageVariants = cva("flex-shrink-0 overflow-hidden rounded-md", {
  variants: {
    style: {
      Simple: "h-12 w-12",
      Banner: "h-16 w-16",
    },
    imageFill: {
      fill: "object-cover",
      contain: "object-contain",
    },
  },
  defaultVariants: {
    style: "Simple",
    imageFill: "contain",
  },
})

export interface TitleWidgetProps extends VariantProps<typeof titleVariants> {
  data?: {
    title?: string
    subtitle?: string
    meta?: string
    image?: string
  }
}

export const TitleWidget = ({ data }: { data: components["schemas"]["TitleWidget"] }) => {
  const { style, data: titleData, design } = data.config
  const { title, subtitle, meta, image } = titleData || {}
  const { imageFill } = design || {}

  // Handle empty content
  if (!title && !subtitle && !meta && !image) {
    return (
      <div className="px-4 py-2">
        <div className="flex items-center justify-center rounded-lg border-2 border-dashed border-ui-border-base bg-ui-bg-subtle p-8 text-center">
          <div className="text-ui-text-muted">
            <svg
              className="text-ui-text-muted mx-auto h-12 w-12"
              stroke="currentColor"
              fill="none"
              viewBox="0 0 48 48"
              aria-hidden="true"
            >
              <path
                d="M8 14v20c0 4.418 7.163 8 16 8 1.381 0 2.721-.087 4-.252M8 14c0 4.418 7.163 8 16 8s16-3.582 16-8M8 14c0-4.418 7.163-8 16-8s16 3.582 16 8m0 0v14m-46-4v4c0 4.418 7.163 8 16 8 1.381 0 2.721-.087 4-.252"
                strokeWidth={2}
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
            <p className="mt-2 text-sm">No title content</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="px-4 py-0.5">
      <div
        className={cn(
          titleVariants({
            style,
          }),
        )}
      >
        {/* Image - positioned on the left for Simple style */}
        {image && (
          <div
            className={cn(
              imageVariants({
                style,
                imageFill,
              }),
            )}
          >
            <img
              src={image}
              alt={title || "Title image"}
              className={cn("h-full w-full rounded-md", imageFill === "fill" ? "object-cover" : "object-contain")}
              onError={(e) => {
                // Handle broken images gracefully
                const target = e.target as HTMLImageElement
                target.style.display = "none"
                const parent = target.parentElement
                if (parent) {
                  parent.innerHTML = `
                    <div class="flex h-full w-full items-center justify-center rounded-md bg-ui-bg-subtle text-ui-text-muted">
                      <svg class="h-6 w-6" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                        <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                      </svg>
                    </div>
                  `
                }
              }}
            />
          </div>
        )}

        {/* Content */}
        <div className="flex min-w-0 flex-1 flex-col gap-1">
          {meta && <p className="text-ui-text-muted text-xs font-medium uppercase">{meta}</p>}
          {title && <h3 className="text-ui-text-base text-lg font-semibold leading-tight tracking-tight">{title}</h3>}
          {subtitle && <p className="text-ui-text-muted text-sm">{subtitle}</p>}
        </div>
      </div>
    </div>
  )
}
