import { Options } from "@/configs"
import { components } from "@saf/sdk"
import { Input, Label, Select, Text } from "@saf/ui"

export const TitleEditor = ({ data }: { data: components["schemas"]["TitleWidget"] }) => {
  return (
    <div className="divide-y">
      <div className="flex flex-col gap-4 p-4">
        <Text className="uppercase" size="xsmall" weight="plus">
          Content
        </Text>
        <div>
          <Input />
        </div>
      </div>
      <div className="flex flex-col gap-4 p-4">
        <Text className="uppercase" size="xsmall" weight="plus">
          Design
        </Text>
        <div className="space-y-4">
          <Label className="flex items-center justify-between gap-4">
            <span>Style</span>
            <div className="w-full max-w-[170px]">
              <Select value={data.config?.title}>
                <Select.Trigger>
                  <Select.Value />
                </Select.Trigger>
                <Select.Content>
                  {styleOption.map((item) => (
                    <Select.Item key={item.value} value={item.value}>
                      {item.label}
                    </Select.Item>
                  ))}
                </Select.Content>
              </Select>
            </div>
          </Label>
          <Label className="flex items-center justify-between gap-4">
            <span>Alignment</span>
            <div className="w-full max-w-[170px]">
              <Select>
                <Select.Trigger>
                  <Select.Value />
                </Select.Trigger>
                <Select.Content>
                  {textAlignOptions.map((item) => (
                    <Select.Item key={item.value} value={item.value}>
                      {item.label}
                    </Select.Item>
                  ))}
                </Select.Content>
              </Select>
            </div>
          </Label>
        </div>
      </div>
    </div>
  )
}

const textAlignOptions: Options = [
  {
    label: "Left",
    value: "left",
  },
  {
    label: "Center",
    value: "center",
  },
  {
    label: "Right",
    value: "right",
  },
]

const styleOption: Options = [
  {
    label: "Large",
    value: "large",
  },
  {
    label: "Regular",
    value: "regular",
  },
  {
    label: "Small",
    value: "small",
  },
  {
    label: "Footnote",
    value: "footnote",
  },
  {
    label: "Meta Text",
    value: "meta_text",
  },
  {
    label: "Headline XSmall",
    value: "headline_xsmall",
  },
  {
    label: "Headline Small",
    value: "headline_small",
  },
  {
    label: "Headline Medium",
    value: "headline_medium",
  },
  {
    label: "Headline Large",
    value: "headline_large",
  },
  {
    label: "Headline XLarge",
    value: "headline_xlarge",
  },
]
