import { components } from "@saf/sdk"
import { Heading, StatusBadge, Text } from "@saf/ui"

export const CollectionWidget = ({ data: _ }: { data: components["schemas"]["CollectionWidget"] }) => {
  const list = [
    {
      title: "Loan Application 1",
      submittedDate: "2023-01-15",
      amount: 5000,
      product: "Personal Loan",
      status: "Pending",
    },
    {
      title: "Loan Application 2",
      submittedDate: "2023-02-10",
      amount: 10000,
      product: "Home Loan",
      status: "Approved",
    },
    {
      title: "Loan Application 3",
      submittedDate: "2023-03-05",
      amount: 7500,
      product: "Car Loan",
      status: "Rejected",
    },
  ]

  return (
    <div className="space-y-4 p-4">
      {list.map((item, index) => (
        <div
          key={index}
          className="flex items-start justify-between gap-4 rounded-lg border bg-ui-bg-base p-4 shadow-sm transition-shadow hover:shadow-md"
        >
          <div className="space-y-0.5">
            <Heading>{item.title}</Heading>
            <Text>{item.submittedDate}</Text>
            <Text>RM{item.amount}</Text>
            <Text>{item.product}</Text>
          </div>
          <StatusBadge
            color={
              {
                Pending: "orange",
                Approved: "green",
                Rejected: "red",
                default: "gray",
              }[item.status] || "default"
            }
          >
            {item.status}
          </StatusBadge>
        </div>
      ))}
    </div>
  )
}
