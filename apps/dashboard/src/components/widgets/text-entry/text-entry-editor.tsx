import { components } from "@saf/sdk"
import { Input, Text } from "@saf/ui"

export const TextEntryEditor = ({ data }: { data: components["schemas"]["TextEntryWidget"] }) => {
  return (
    <div className="divide-y">
      <div className="flex flex-col gap-4 p-4">
        <Text className="uppercase" size="xsmall" weight="plus">
          Content
        </Text>
        <div>
          <Input value={data.name} />
        </div>
      </div>
    </div>
  )
}
