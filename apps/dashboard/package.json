{"name": "dashboard", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "pnpm lint && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@ariakit/react": "^0.4.15", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^5.0.1", "@medusajs/icons": "2.7.1", "@medusajs/ui-preset": "2.7.1", "@saf/sdk": "workspace:*", "@saf/ui": "workspace:*", "@tabler/icons-react": "^3.31.0", "@tanstack/react-query": "^5.76.1", "@tanstack/react-table": "8.20.5", "@uiw/react-json-view": "2.0.0-alpha.32", "class-variance-authority": "^0.7.1", "copy-to-clipboard": "^3.3.3", "date-fns": "^4.1.0", "i18next": "^25.1.2", "i18next-browser-languagedetector": "^8.1.0", "match-sorter": "^8.0.1", "motion": "^12.11.3", "openapi-fetch": "^0.14.0", "openapi-msw": "^1.2.0", "openapi-react-query": "^0.5.0", "radix-ui": "1.1.2", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.56.3", "react-i18next": "^15.5.1", "react-jwt": "^1.3.0", "react-router": "^7.6.0", "react-router-dom": "^7.6.0", "tailwind-merge": "^2.2.1", "use-debounce": "^10.0.4", "zod": "^3.24.4", "zustand": "^5.0.4"}, "devDependencies": {"@types/node": "^22.15.18", "@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.17", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "msw": "^2.8.2", "openapi-typescript": "^7.8.0", "postcss": "^8.4.35", "prettier": "^3.1.1", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^3.4.1", "typescript": "^5.3.3", "vite": "^5.1.0"}, "msw": {"workerDirectory": ["public"]}}